# 音频歌词对轴程序

这是一个音频歌词对轴程序，可以将歌词与音频进行时间同步，并提供一个美观的网页播放器来展示。

## 文件说明

- `audio.wav` - AI生成的音乐文件
- `歌词.md` - 原始歌词文件，包含段落标记
- `lyrics_sync.py` - 歌词对轴脚本
- `歌词_sync.json` - 生成的同步歌词文件
- `index.html` - 音乐播放器网页
- `requirements.txt` - Python依赖包

## 使用方法

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 生成对轴歌词
```bash
python lyrics_sync.py
```

### 3. 启动网页服务器
```bash
python -m http.server 8000
```

### 4. 打开浏览器
访问 `http://localhost:8000` 即可使用音乐播放器

## 功能特点

### 歌词对轴脚本 (lyrics_sync.py)
- 自动分析音频时长
- 解析带标记的歌词文件
- 智能分配时间轴
- 生成JSON格式的同步歌词

### 网页播放器 (index.html)
- 🎵 美观的唱片风格界面
- 🎶 旋转的专辑封面动画
- 📝 实时高亮显示当前歌词
- ⏯️ 完整的播放控制
- 📊 可点击的进度条
- ⌨️ 空格键播放/暂停
- 📱 响应式设计

## 歌词格式

歌词文件使用Markdown格式，支持以下段落标记：
- `[intro-short]` - 短前奏
- `[verse]` - 主歌
- `[chorus]` - 副歌
- `[bridge]` - 桥段
- `[outro-short]` - 短尾奏

示例：
```markdown
[intro-short]
[verse]
今天的心情糟透了
客户的电话响个不停
[chorus]
烦烦烦烦到爆炸
脑子像被雷劈啦
```

## 技术栈

- **后端**: Python + librosa (音频分析)
- **前端**: HTML5 + CSS3 + JavaScript
- **音频**: HTML5 Audio API
- **样式**: 现代CSS动画和毛玻璃效果

## 自定义配置

可以在 `lyrics_sync.py` 中调整以下参数：
- 各段落的默认时长分配
- 时间轴计算算法
- 输出文件格式

## 浏览器兼容性

支持所有现代浏览器：
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+