<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐歌词播放器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
        }

        .player-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .album-cover {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            margin: 0 auto 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            animation: rotate 20s linear infinite;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .album-cover.playing {
            animation-play-state: running;
        }

        .album-cover.paused {
            animation-play-state: paused;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .song-info {
            text-align: center;
            margin-bottom: 20px;
        }

        .song-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .song-artist {
            font-size: 16px;
            opacity: 0.8;
        }

        .lyrics-container {
            height: 200px;
            overflow-y: auto;
            margin: 20px 0;
            padding: 15px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
        }

        .lyrics-container::-webkit-scrollbar {
            width: 6px;
        }

        .lyrics-container::-webkit-scrollbar-track {
            background: transparent;
        }

        .lyrics-container::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .lyric-line {
            padding: 8px 0;
            font-size: 16px;
            line-height: 1.5;
            opacity: 0.6;
            transition: all 0.3s ease;
            text-align: center;
        }

        .lyric-line.active {
            opacity: 1;
            font-weight: bold;
            font-size: 18px;
            color: #ffd700;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
            transform: scale(1.05);
        }

        .progress-container {
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
            cursor: pointer;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #ffd700);
            width: 0%;
            transition: width 0.1s ease;
        }

        .time-info {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            margin-top: 5px;
            opacity: 0.8;
        }

        .controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin-top: 20px;
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .play-btn {
            width: 60px;
            height: 60px;
            font-size: 24px;
            background: rgba(255, 255, 255, 0.3);
        }

        .loading {
            text-align: center;
            padding: 20px;
            font-size: 16px;
        }

        .error {
            text-align: center;
            padding: 20px;
            color: #ff6b6b;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="player-container">
        <div class="album-cover paused" id="albumCover">
            🎵
        </div>
        
        <div class="song-info">
            <div class="song-title">工作烦恼之歌</div>
            <div class="song-artist">AI 创作</div>
        </div>
        
        <div class="lyrics-container" id="lyricsContainer">
            <div class="loading">正在加载歌词...</div>
        </div>
        
        <div class="progress-container">
            <div class="progress-bar" id="progressBar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="time-info">
                <span id="currentTime">0:00</span>
                <span id="totalTime">0:00</span>
            </div>
        </div>
        
        <div class="controls">
            <button class="control-btn" id="prevBtn">⏮</button>
            <button class="control-btn play-btn" id="playBtn">▶</button>
            <button class="control-btn" id="nextBtn">⏭</button>
        </div>
        
        <audio id="audioPlayer" preload="auto">
            <source src="audio.wav" type="audio/wav">
            您的浏览器不支持音频播放。
        </audio>
    </div>

    <script>
        class MusicPlayer {
            constructor() {
                this.audio = document.getElementById('audioPlayer');
                this.playBtn = document.getElementById('playBtn');
                this.albumCover = document.getElementById('albumCover');
                this.lyricsContainer = document.getElementById('lyricsContainer');
                this.progressBar = document.getElementById('progressBar');
                this.progressFill = document.getElementById('progressFill');
                this.currentTimeEl = document.getElementById('currentTime');
                this.totalTimeEl = document.getElementById('totalTime');
                
                this.lyrics = [];
                this.currentLyricIndex = -1;
                this.isPlaying = false;
                
                this.init();
            }
            
            async init() {
                await this.loadLyrics();
                this.setupEventListeners();
                this.renderLyrics();
            }
            
            async loadLyrics() {
                try {
                    const response = await fetch('歌词_sync.json');
                    if (!response.ok) {
                        throw new Error('歌词文件加载失败');
                    }
                    const data = await response.json();
                    this.lyrics = data.lyrics;
                    console.log('歌词加载成功:', this.lyrics.length, '行');
                } catch (error) {
                    console.error('加载歌词失败:', error);
                    this.lyricsContainer.innerHTML = '<div class="error">歌词加载失败，请确保歌词文件存在</div>';
                }
            }
            
            setupEventListeners() {
                // 播放/暂停按钮
                this.playBtn.addEventListener('click', () => this.togglePlay());
                
                // 音频事件
                this.audio.addEventListener('loadedmetadata', () => {
                    this.totalTimeEl.textContent = this.formatTime(this.audio.duration);
                });
                
                this.audio.addEventListener('timeupdate', () => {
                    this.updateProgress();
                    this.updateLyrics();
                });
                
                this.audio.addEventListener('ended', () => {
                    this.isPlaying = false;
                    this.playBtn.textContent = '▶';
                    this.albumCover.classList.remove('playing');
                    this.albumCover.classList.add('paused');
                });
                
                // 进度条点击
                this.progressBar.addEventListener('click', (e) => {
                    const rect = this.progressBar.getBoundingClientRect();
                    const percent = (e.clientX - rect.left) / rect.width;
                    this.audio.currentTime = percent * this.audio.duration;
                });
                
                // 键盘控制
                document.addEventListener('keydown', (e) => {
                    if (e.code === 'Space') {
                        e.preventDefault();
                        this.togglePlay();
                    }
                });
            }
            
            togglePlay() {
                if (this.isPlaying) {
                    this.audio.pause();
                    this.playBtn.textContent = '▶';
                    this.albumCover.classList.remove('playing');
                    this.albumCover.classList.add('paused');
                } else {
                    this.audio.play();
                    this.playBtn.textContent = '⏸';
                    this.albumCover.classList.remove('paused');
                    this.albumCover.classList.add('playing');
                }
                this.isPlaying = !this.isPlaying;
            }
            
            updateProgress() {
                if (this.audio.duration) {
                    const percent = (this.audio.currentTime / this.audio.duration) * 100;
                    this.progressFill.style.width = percent + '%';
                    this.currentTimeEl.textContent = this.formatTime(this.audio.currentTime);
                }
            }
            
            updateLyrics() {
                const currentTime = this.audio.currentTime;
                let newIndex = -1;
                
                for (let i = 0; i < this.lyrics.length; i++) {
                    if (currentTime >= this.lyrics[i].start && currentTime < this.lyrics[i].end) {
                        newIndex = i;
                        break;
                    }
                }
                
                if (newIndex !== this.currentLyricIndex) {
                    this.currentLyricIndex = newIndex;
                    this.highlightLyric(newIndex);
                }
            }
            
            highlightLyric(index) {
                const lyricLines = this.lyricsContainer.querySelectorAll('.lyric-line');
                
                lyricLines.forEach((line, i) => {
                    if (i === index) {
                        line.classList.add('active');
                        // 滚动到当前歌词
                        line.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    } else {
                        line.classList.remove('active');
                    }
                });
            }
            
            renderLyrics() {
                if (this.lyrics.length === 0) {
                    return;
                }
                
                // 过滤掉标签行（如[intro-short]等）
                const filteredLyrics = this.lyrics.filter(lyric => 
                    !lyric.text.startsWith('[') || !lyric.text.endsWith(']')
                );
                
                const lyricsHTML = filteredLyrics.map((lyric, index) => 
                    `<div class="lyric-line" data-index="${index}">${lyric.text}</div>`
                ).join('');
                
                this.lyricsContainer.innerHTML = lyricsHTML;
            }
            
            formatTime(seconds) {
                const mins = Math.floor(seconds / 60);
                const secs = Math.floor(seconds % 60);
                return `${mins}:${secs.toString().padStart(2, '0')}`;
            }
        }
        
        // 初始化播放器
        document.addEventListener('DOMContentLoaded', () => {
            new MusicPlayer();
        });
    </script>
</body>
</html>