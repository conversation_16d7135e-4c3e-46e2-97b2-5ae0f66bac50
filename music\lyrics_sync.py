import librosa
import json
import re
from typing import List, Dict, Tuple

class LyricsSynchronizer:
    def __init__(self, audio_path: str, lyrics_path: str):
        self.audio_path = audio_path
        self.lyrics_path = lyrics_path
        self.audio_duration = None
        self.lyrics_data = []
        
    def load_audio_info(self):
        """加载音频信息"""
        try:
            y, sr = librosa.load(self.audio_path)
            self.audio_duration = librosa.get_duration(y=y, sr=sr)
            print(f"音频时长: {self.audio_duration:.2f} 秒")
        except Exception as e:
            print(f"加载音频失败: {e}")
            return False
        return True
    
    def parse_lyrics(self):
        """解析歌词文件"""
        try:
            # 尝试不同的编码方式
            encodings = ['utf-8', 'utf-8-sig', 'gbk', 'cp1252']
            content = None
            
            for encoding in encodings:
                try:
                    with open(self.lyrics_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    print(f"使用编码 {encoding} 成功读取文件")
                    break
                except UnicodeDecodeError:
                    print(f"编码 {encoding} 读取失败")
                    continue
            
            if content is None:
                print("所有编码方式都失败")
                return []
            
            print(f"原始内容长度: {len(content)}")
            print(f"原始内容前100字符: {repr(content[:100])}")
            
            # 使用正则表达式分割，保留分隔符
            parts = re.split(r'(\[\w+(?:-\w+)?\])', content)
            parts = [p.strip() for p in parts if p.strip()]
            
            print(f"分割后的部分: {parts}")
            
            lyrics_sections = []
            current_section = None
            
            for part in parts:
                # 检查是否是段落标记
                section_match = re.match(r'\[(\w+(?:-\w+)?)\]', part)
                if section_match:
                    # 如果之前有未完成的段落，先保存
                    if current_section:
                        lyrics_sections.append({
                            'type': current_section,
                            'lines': []
                        })
                    current_section = section_match.group(1)
                else:
                    # 这是歌词内容
                    if current_section:
                        lines = [line.strip() for line in part.split('\n') if line.strip()]
                        lyrics_sections.append({
                            'type': current_section,
                            'lines': lines
                        })
                        current_section = None
            
            # 处理最后一个段落（如果没有内容）
            if current_section:
                lyrics_sections.append({
                    'type': current_section,
                    'lines': []
                })
            
            print(f"解析到 {len(lyrics_sections)} 个歌词段落")
            for section in lyrics_sections:
                print(f"- {section['type']}: {len(section['lines'])} 行")
            
            return lyrics_sections
        except Exception as e:
            print(f"解析歌词失败: {e}")
            return []
    
    def calculate_timing(self, lyrics_sections: List[Dict]) -> List[Dict]:
        """计算歌词时间轴"""
        if not self.audio_duration:
            return []
        
        # 定义各段落的时间分配比例
        section_durations = {
            'intro-short': 8,  # 8秒
            'verse': 25,       # 25秒
            'chorus': 20,      # 20秒
            'bridge': 15,      # 15秒
            'outro-short': 5   # 5秒
        }
        
        # 计算总预期时长
        total_expected = sum(section_durations.get(section['type'], 20) for section in lyrics_sections)
        
        # 按比例调整到实际音频时长
        scale_factor = self.audio_duration / total_expected
        
        timed_lyrics = []
        current_time = 0
        
        for section in lyrics_sections:
            section_type = section['type']
            lines = section['lines']
            
            # 计算这个段落的实际时长
            section_duration = section_durations.get(section_type, 20) * scale_factor
            
            if lines:
                # 将时间平均分配给每行歌词
                time_per_line = section_duration / len(lines)
                
                for i, line in enumerate(lines):
                    start_time = current_time + (i * time_per_line)
                    end_time = start_time + time_per_line
                    
                    timed_lyrics.append({
                        'start': round(max(0, start_time - 1.5), 2),  # 提前1.5秒
                        'end': round(max(0, end_time - 1.5), 2),
                        'text': line,
                        'section': section_type
                    })
            else:
                # 空段落（如intro-short, outro-short）- 跳过标签
                timed_lyrics.append({
                    'start': round(max(0, current_time - 1.5), 2),  # 提前1.5秒
                    'end': round(max(0, current_time + section_duration - 1.5), 2),
                    'text': '',  # 空内容，不显示标签
                    'section': section_type
                })
            
            current_time += section_duration
        
        return timed_lyrics
    
    def generate_sync_file(self, output_path: str = None):
        """生成同步歌词文件"""
        if not self.load_audio_info():
            return False
        
        lyrics_sections = self.parse_lyrics()
        if not lyrics_sections:
            print("没有找到有效的歌词内容")
            return False
        
        timed_lyrics = self.calculate_timing(lyrics_sections)
        
        if not output_path:
            output_path = self.lyrics_path.replace('.md', '_sync.json')
        
        # 保存为JSON格式
        sync_data = {
            'audio_duration': self.audio_duration,
            'lyrics': timed_lyrics
        }
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(sync_data, f, ensure_ascii=False, indent=2)
            
            print(f"同步歌词已保存到: {output_path}")
            print(f"共生成 {len(timed_lyrics)} 行歌词")
            
            # 打印预览
            print("\n歌词预览:")
            for lyric in timed_lyrics[:5]:
                print(f"{lyric['start']:6.2f}s - {lyric['end']:6.2f}s: {lyric['text']}")
            if len(timed_lyrics) > 5:
                print("...")
            
            return True
        except Exception as e:
            print(f"保存文件失败: {e}")
            return False

if __name__ == "__main__":
    # 使用示例
    import os
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    audio_file = os.path.join(current_dir, "audio.wav")
    lyrics_file = os.path.join(current_dir, "歌词.md")
    
    print(f"音频文件路径: {audio_file}")
    print(f"歌词文件路径: {lyrics_file}")
    print(f"音频文件存在: {os.path.exists(audio_file)}")
    print(f"歌词文件存在: {os.path.exists(lyrics_file)}")
    
    synchronizer = LyricsSynchronizer(audio_file, lyrics_file)
    synchronizer.generate_sync_file()